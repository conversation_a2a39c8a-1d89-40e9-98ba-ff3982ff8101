// Copyright Isto Inc.

using Isto.GTW.Data;
using Isto.GTW.Leaderboards;
using Isto.GTW.UI;
using Steamworks;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.GTW.Managers
{
    public class GTWSteamLeaderboard : MonoBehaviour, ILeaderboard
    {



        // OTHER FIELDS

        // Steamworks leaderboard name
        private static string LEADERBOARD_NAME = "Doinkler Special Highest Checkpoint";

        // Steam call results
        private CallResult<LeaderboardScoresDownloaded_t> _downloadScoresResult;
        private CallResult<LeaderboardFindResult_t> _findLeaderboardResult;
        private CallResult<LeaderboardScoreUploaded_t> _uploadScoreResult;

        private SteamLeaderboard_t _steamLeaderboard;
        private List<LeaderboardEntryData> _leaderboardEntries = new List<LeaderboardEntryData>();

        private bool _leaderboardFound;
        private int _highestCheckpoint;

        // Rate limiting fields
        private static readonly int MAX_UPLOADS_PER_WINDOW = 10;
        private static readonly float RATE_LIMIT_WINDOW_SECONDS = 600f; // 10 minutes

        private List<float> _uploadTimestamps = new List<float>();
        private Queue<int> _pendingUploads = new Queue<int>();
        private bool _hasOutstandingUpload;

        // Test mode fields
        private int _testCurrentCheckpoint = 1;
        private bool _testModeActive = false;
        private bool _enableTestMode = true;
        private float _testUploadInterval = 30f; // seconds between test uploads
        private float _testStartDelay = 45f; // seconds to wait before starting test
        private int _testMaxCheckpoint = 50; // maximum checkpoint to test


        // PROPERTIES

        public string LeaderboardName => SteamFriends.GetPersonaName();
        public List<LeaderboardEntryData> LeaderboardEntries => _leaderboardEntries;


        // EVENTS

        public event Action OnCheckpointsDownloaded;


        // LIFECYCLE EVENTS

        private void Start()
        {
            if (!SteamManager.Initialized)
            {
                Debug.LogError("[LeaderboardManager] SteamManager not initialized. Cannot find or create leaderboard.");
                return;
            }

            _findLeaderboardResult = CallResult<LeaderboardFindResult_t>.Create(OnFindLeaderboard);
            _uploadScoreResult = CallResult<LeaderboardScoreUploaded_t>.Create(OnUploadScore);
            _downloadScoresResult = CallResult<LeaderboardScoresDownloaded_t>.Create(OnDownloadScores);

            SetupLeaderboard();

            // Start test mode if enabled
            if (_enableTestMode)
            {
                StartTestMode();
            }
        }

        private void OnDestroy()
        {
            _findLeaderboardResult?.Cancel();
            _uploadScoreResult?.Cancel();
            _downloadScoresResult?.Cancel();

            // Stop test mode
            if (_testModeActive)
            {
                StopAllCoroutines();
                _testModeActive = false;
            }
        }

        // EVENT HANDLING

        private void OnFindLeaderboard(LeaderboardFindResult_t result, bool isIOFailure)
        {
            if (isIOFailure || result.m_bLeaderboardFound == 0)
            {
                Debug.LogError($"[LeaderboardManager] Failed to find or create leaderboard '{LEADERBOARD_NAME}'. IOFailure? {isIOFailure}");
                return;
            }

            _steamLeaderboard = result.m_hSteamLeaderboard;
            _leaderboardFound = true;
            Debug.Log($"[LeaderboardManager] Found or created leaderboard '{LEADERBOARD_NAME}' (Handle: {_steamLeaderboard.m_SteamLeaderboard}).");
        }

        private void OnUploadScore(LeaderboardScoreUploaded_t result, bool isIOFailure)
        {
            _hasOutstandingUpload = false;

            if (isIOFailure || result.m_bSuccess == 0)
            {
                Debug.LogError($"[LeaderboardManager] Score upload failed. IOFailure? {isIOFailure}");
            }
            else
            {
                Debug.Log($"[LeaderboardManager] Upload succeeded. New rank: {result.m_nGlobalRankNew}, Score: {result.m_nScore}");
            }

            // Process any pending uploads
            ProcessPendingUploads();
        }

        private void OnDownloadScores(LeaderboardScoresDownloaded_t result, bool isIOFailure)
        {
            if (isIOFailure)
            {
                Debug.LogError($"[LeaderboardManager] Failed to download leaderboard entries. IOFailure? {isIOFailure}");
                return;
            }

            int count = result.m_cEntryCount;
            List<LeaderboardEntryData> entries = new List<LeaderboardEntryData>(count);

            for (int i = 0; i < count; i++)
            {
                LeaderboardEntry_t entry;
                SteamUserStats.GetDownloadedLeaderboardEntry(
                    result.m_hSteamLeaderboardEntries,
                    i,
                    out entry,
                    null,
                    0
                );

                string playerName = SteamFriends.GetFriendPersonaName(entry.m_steamIDUser);
                int globalRank = entry.m_nGlobalRank;
                int localRank = i + 1;

                Texture2D avatarTex = GetPlayerAvatar(entry);

                LeaderboardEntryData data = new LeaderboardEntryData(
                    playerName,
                    entry.m_nScore,
                    globalRank,
                    localRank,
                    avatarTex
                );
                entries.Add(data);
            }

            GetPlayersHighestScoreFromLeaderboard(entries);

            _leaderboardEntries = entries;

            OnCheckpointsDownloaded?.Invoke();
        }


        // ACCESSORS

        private static Texture2D GetPlayerAvatar(LeaderboardEntry_t entry)
        {
            SteamFriends.RequestUserInformation(entry.m_steamIDUser, true);

            int avatarHandle = SteamFriends.GetLargeFriendAvatar(entry.m_steamIDUser);
            Texture2D avatarTextyre = null;

            if (avatarHandle != -1)
            {
                if (SteamUtils.GetImageSize(avatarHandle, out uint width, out uint height))
                {
                    byte[] raw = new byte[width * height * 4]; // We multiply by 4 as RGBA is 4 bytes per pixel
                    if (SteamUtils.GetImageRGBA(avatarHandle, raw, raw.Length))
                    {
                        avatarTextyre = new Texture2D((int)width, (int)height, TextureFormat.RGBA32, false);
                        avatarTextyre.LoadRawTextureData(raw);
                        avatarTextyre.Apply();

                        // Since the coordinates are different between Unity and Steam, we need to flip the texture vertically
                        avatarTextyre = GTWUtils.FlipTextureVertically(avatarTextyre);

                    }
                }
            }

            return avatarTextyre;
        }

        private void GetPlayersHighestScoreFromLeaderboard(List<LeaderboardEntryData> entries)
        {
            LeaderboardEntryData playerEntryData = entries.Find(x => x.PlayerName == LeaderboardName);
            if(playerEntryData != null)
            {
                _highestCheckpoint = playerEntryData.HighestCheckpoint;
            }
            else
            {
                _highestCheckpoint = 0;
            }
        }


        // OTHER METHODS

        public void UploadHighestCheckpoint(int score)
        {
            if (!_leaderboardFound)
            {
                Debug.LogError("[LeaderboardManager] UploadScore called too early. Leaderboard not yet found.");
                return;
            }

            if (score > _highestCheckpoint)
            {
                _highestCheckpoint = score;

                if (CanUploadImmediately())
                {
                    PerformUpload(score);
                }
                else
                {
                    QueueUpload(score);
                }
            }
        }

        public void DownloadFriendsEntries(int startRank, int endRank)
        {
            if (!_leaderboardFound)
            {
                Debug.LogError("[LeaderboardManager] DownloadTopEntries called too early. Leaderboard not yet found.");
                return;
            }

            SteamAPICall_t call = SteamUserStats.DownloadLeaderboardEntries(
                _steamLeaderboard,
                ELeaderboardDataRequest.k_ELeaderboardDataRequestFriends,
                startRank,
                endRank
            );

            _downloadScoresResult.Set(call);
            Debug.Log($"[LeaderboardManager] Requesting entries {startRank}–{endRank}...");
        }

        private void SetupLeaderboard()
        {
            SteamAPICall_t handle = SteamUserStats.FindOrCreateLeaderboard(
                LEADERBOARD_NAME,
                ELeaderboardSortMethod.k_ELeaderboardSortMethodDescending,
                ELeaderboardDisplayType.k_ELeaderboardDisplayTypeNumeric
            );
            _findLeaderboardResult.Set(handle);

            Debug.Log($"[LeaderboardManager] Attempting to find or create leaderboard '{LEADERBOARD_NAME}'...");
        }


        // OTHER: Rate Limiting

        /// <summary>
        /// Checks if an upload can be performed immediately based on rate limiting rules.
        /// </summary>
        /// <returns>True if upload can proceed immediately.</returns>
        private bool CanUploadImmediately()
        {
            if (_hasOutstandingUpload)
            {
                return false;
            }

            CleanupOldTimestamps();
            return _uploadTimestamps.Count < MAX_UPLOADS_PER_WINDOW;
        }

        /// <summary>
        /// Removes upload timestamps that are outside the rate limiting window.
        /// </summary>
        private void CleanupOldTimestamps()
        {
            float currentTime = Time.time;
            float cutoffTime = currentTime - RATE_LIMIT_WINDOW_SECONDS;

            for (int i = _uploadTimestamps.Count - 1; i >= 0; i--)
            {
                if (_uploadTimestamps[i] < cutoffTime)
                {
                    _uploadTimestamps.RemoveAt(i);
                }
            }
        }

        /// <summary>
        /// Performs the actual upload to Steam and tracks the timestamp.
        /// </summary>
        /// <param name="score">The score to upload.</param>
        private void PerformUpload(int score)
        {
            _hasOutstandingUpload = true;
            _uploadTimestamps.Add(Time.time);

            SteamAPICall_t call = SteamUserStats.UploadLeaderboardScore(
                _steamLeaderboard,
                ELeaderboardUploadScoreMethod.k_ELeaderboardUploadScoreMethodForceUpdate,
                score,
                null,
                0
            );
            _uploadScoreResult.Set(call);
            Debug.Log($"[LeaderboardManager] Uploading score {score}...");
        }

        /// <summary>
        /// Queues an upload for later processing when rate limits allow.
        /// </summary>
        /// <param name="score">The score to queue for upload.</param>
        private void QueueUpload(int score)
        {
            // Only keep the highest score in the queue to avoid unnecessary uploads
            if (_pendingUploads.Count == 0 || score > _pendingUploads.Peek())
            {
                _pendingUploads.Clear();
                _pendingUploads.Enqueue(score);
                Debug.Log($"[LeaderboardManager] Queued score {score} for upload (rate limited).");
            }
        }

        /// <summary>
        /// Processes any pending uploads if rate limits allow.
        /// </summary>
        private void ProcessPendingUploads()
        {
            if (_pendingUploads.Count > 0 && CanUploadImmediately())
            {
                int score = _pendingUploads.Dequeue();
                PerformUpload(score);
            }
        }


        // OTHER: Test Mode

        /// <summary>
        /// Starts the rate limiting test mode.
        /// </summary>
        private void StartTestMode()
        {
            if (_testModeActive)
            {
                return;
            }

            _testModeActive = true;
            _testCurrentCheckpoint = 1;
            Debug.Log($"[LeaderboardManager] Starting rate limiting test mode. Will upload checkpoints 1-{_testMaxCheckpoint} every {_testUploadInterval}s after {_testStartDelay}s delay.");

            StartCoroutine(TestModeCoroutine());
        }

        /// <summary>
        /// Stops the rate limiting test mode.
        /// </summary>
        private void StopTestMode()
        {
            if (!_testModeActive)
            {
                return;
            }

            _testModeActive = false;
            StopAllCoroutines();
            Debug.Log("[LeaderboardManager] Rate limiting test mode stopped.");
        }

        /// <summary>
        /// Coroutine that handles the test mode upload sequence.
        /// </summary>
        private System.Collections.IEnumerator TestModeCoroutine()
        {
            // Wait for initial delay
            Debug.Log($"[LeaderboardManager] Test mode waiting {_testStartDelay} seconds before starting uploads...");
            yield return new WaitForSeconds(_testStartDelay);

            // Upload checkpoints in sequence
            while (_testModeActive && _testCurrentCheckpoint <= _testMaxCheckpoint)
            {
                Debug.Log($"[LeaderboardManager] Test mode uploading checkpoint {_testCurrentCheckpoint}");
                UploadHighestCheckpoint(_testCurrentCheckpoint);

                _testCurrentCheckpoint++;

                if (_testCurrentCheckpoint <= _testMaxCheckpoint)
                {
                    yield return new WaitForSeconds(_testUploadInterval);
                }
            }

            Debug.Log("[LeaderboardManager] Test mode completed all uploads.");
            _testModeActive = false;
        }

        /// <summary>
        /// Manually trigger test mode from inspector or code.
        /// </summary>
        [ContextMenu("Start Rate Limiting Test")]
        public void StartRateLimitingTest()
        {
            if (!_testModeActive)
            {
                StartTestMode();
            }
            else
            {
                Debug.Log("[LeaderboardManager] Test mode is already running.");
            }
        }

        /// <summary>
        /// Manually stop test mode from inspector or code.
        /// </summary>
        [ContextMenu("Stop Rate Limiting Test")]
        public void StopRateLimitingTest()
        {
            StopTestMode();
        }

        /// <summary>
        /// Get current rate limiting status for debugging.
        /// </summary>
        [ContextMenu("Show Rate Limiting Status")]
        public void ShowRateLimitingStatus()
        {
            CleanupOldTimestamps();
            Debug.Log($"[LeaderboardManager] Rate Limiting Status:");
            Debug.Log($"  - Uploads in current window: {_uploadTimestamps.Count}/{MAX_UPLOADS_PER_WINDOW}");
            Debug.Log($"  - Has outstanding upload: {_hasOutstandingUpload}");
            Debug.Log($"  - Pending uploads: {_pendingUploads.Count}");
            Debug.Log($"  - Can upload immediately: {CanUploadImmediately()}");

            if (_uploadTimestamps.Count > 0)
            {
                float oldestTimestamp = _uploadTimestamps[0];
                float timeUntilOldestExpires = (oldestTimestamp + RATE_LIMIT_WINDOW_SECONDS) - Time.time;
                Debug.Log($"  - Time until oldest upload expires: {timeUntilOldestExpires:F1} seconds");
            }
        }
    }
}